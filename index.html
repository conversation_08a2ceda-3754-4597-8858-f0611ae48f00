<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <link rel="stylesheet" href="index.css"></link>
</head>

<body>
    <div class="iphone-wrapper">
        <div class="iphone-mock">
            <div class="iphone-screen">
                <div class="dynamic-island"></div>
                <div class="app-content">
                    <div class="app-grid-container">
                        <div class="app-grid">
                            <div class="app-icon" tabindex="0">
                                <div class="app-icon-inner" style="background-color: rgb(52, 199, 89);"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="lucide lucide-message-square-icon lucide-message-square">
                                        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                                    </svg></div>
                                <p class="app-label">Messages</p>
                            </div>
                            <div class="app-icon" tabindex="0">
                                <div class="app-icon-inner" style="background-color: rgb(255, 149, 0);"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-calendar-icon lucide-calendar">
                                        <path d="M8 2v4"></path>
                                        <path d="M16 2v4"></path>
                                        <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                                        <path d="M3 10h18"></path>
                                    </svg></div>
                                <p class="app-label">Calendar</p>
                            </div>
                            <div class="app-icon" tabindex="0">
                                <div class="app-icon-inner" style="background-color: rgb(175, 82, 222);"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-image-icon lucide-image">
                                        <rect width="18" height="18" x="3" y="3" rx="2" ry="2"></rect>
                                        <circle cx="9" cy="9" r="2"></circle>
                                        <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"></path>
                                    </svg></div>
                                <p class="app-label">Photos</p>
                            </div>
                            <div class="app-icon" tabindex="0">
                                <div class="app-icon-inner" style="background-color: rgb(90, 200, 250);"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-camera-icon lucide-camera">
                                        <path
                                            d="M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z">
                                        </path>
                                        <circle cx="12" cy="13" r="3"></circle>
                                    </svg></div>
                                <p class="app-label">Camera</p>
                            </div>
                            <div class="app-icon" tabindex="0">
                                <div class="app-icon-inner" style="background-color: rgb(255, 45, 85);"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-map-icon lucide-map">
                                        <path
                                            d="M14.106 5.553a2 2 0 0 0 1.788 0l3.659-1.83A1 1 0 0 1 21 4.619v12.764a1 1 0 0 1-.553.894l-4.553 2.277a2 2 0 0 1-1.788 0l-4.212-2.106a2 2 0 0 0-1.788 0l-3.659 1.83A1 1 0 0 1 3 19.381V6.618a1 1 0 0 1 .553-.894l4.553-2.277a2 2 0 0 1 1.788 0z">
                                        </path>
                                        <path d="M15 5.764v15"></path>
                                        <path d="M9 3.236v15"></path>
                                    </svg></div>
                                <p class="app-label">Maps</p>
                            </div>
                            <div class="app-icon" tabindex="0">
                                <div class="app-icon-inner" style="background-color: rgb(255, 149, 0);"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-cloud-icon lucide-cloud">
                                        <path d="M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z"></path>
                                    </svg></div>
                                <p class="app-label">Weather</p>
                            </div>
                            <div class="app-icon" tabindex="0">
                                <div class="app-icon-inner" style="background-color: rgb(0, 0, 0);"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-wallet-icon lucide-wallet">
                                        <path
                                            d="M19 7V4a1 1 0 0 0-1-1H5a2 2 0 0 0 0 4h15a1 1 0 0 1 1 1v4h-3a2 2 0 0 0 0 4h3a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1">
                                        </path>
                                        <path d="M3 5v14a2 2 0 0 0 2 2h15a1 1 0 0 0 1-1v-4"></path>
                                    </svg></div>
                                <p class="app-label">Wallet</p>
                            </div>
                            <div class="app-icon" tabindex="0">
                                <div class="app-icon-inner" style="background-color: rgb(255, 204, 0);"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-file-text-icon lucide-file-text">
                                        <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path>
                                        <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
                                        <path d="M10 9H8"></path>
                                        <path d="M16 13H8"></path>
                                        <path d="M16 17H8"></path>
                                    </svg></div>
                                <p class="app-label">Notes</p>
                            </div>
                            <div class="app-icon" tabindex="0">
                                <div class="app-icon-inner" style="background-color: rgb(255, 59, 48);"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="lucide lucide-square-check-big-icon lucide-square-check-big">
                                        <path d="M21 10.5V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h12.5"></path>
                                        <path d="m9 11 3 3L22 4"></path>
                                    </svg></div>
                                <p class="app-label">Reminders</p>
                            </div>
                            <div class="app-icon" tabindex="0">
                                <div class="app-icon-inner" style="background-color: rgb(88, 86, 214);"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-clock-icon lucide-clock">
                                        <circle cx="12" cy="12" r="10"></circle>
                                        <polyline points="12 6 12 12 16 14"></polyline>
                                    </svg></div>
                                <p class="app-label">Clock</p>
                            </div>
                            <div class="app-icon" tabindex="0">
                                <div class="app-icon-inner" style="background-color: rgb(255, 45, 85);"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-newspaper-icon lucide-newspaper">
                                        <path d="M15 18h-5"></path>
                                        <path d="M18 14h-8"></path>
                                        <path
                                            d="M4 22h16a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v16a2 2 0 0 1-4 0v-9a2 2 0 0 1 2-2h2">
                                        </path>
                                        <rect x="10" y="6" width="8" height="4" rx="1"></rect>
                                    </svg></div>
                                <p class="app-label">News</p>
                            </div>
                            <div class="app-icon" tabindex="0">
                                <div class="app-icon-inner" style="background-color: rgb(0, 122, 255);"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="lucide lucide-shopping-bag-icon lucide-shopping-bag">
                                        <path d="M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z"></path>
                                        <path d="M3 6h18"></path>
                                        <path d="M16 10a4 4 0 0 1-8 0"></path>
                                    </svg></div>
                                <p class="app-label">App Store</p>
                            </div>
                            <div class="app-icon" tabindex="0">
                                <div class="app-icon-inner" style="background-color: rgb(255, 45, 85);"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-heart-icon lucide-heart">
                                        <path
                                            d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z">
                                        </path>
                                    </svg></div>
                                <p class="app-label">Health</p>
                            </div>
                            <div class="app-icon" tabindex="0">
                                <div class="app-icon-inner" style="background-color: rgb(142, 142, 147);"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-settings-icon lucide-settings">
                                        <path
                                            d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z">
                                        </path>
                                        <circle cx="12" cy="12" r="3"></circle>
                                    </svg></div>
                                <p class="app-label">Settings</p>
                            </div>
                            <div class="app-icon" tabindex="0">
                                <div class="app-icon-inner" style="background-color: rgb(0, 122, 255);"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-compass-icon lucide-compass">
                                        <path
                                            d="m16.24 7.76-1.804 5.411a2 2 0 0 1-1.265 1.265L7.76 16.24l1.804-5.411a2 2 0 0 1 1.265-1.265z">
                                        </path>
                                        <circle cx="12" cy="12" r="10"></circle>
                                    </svg></div>
                                <p class="app-label">Safari</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="iphone-home-indicator"></div>
                <div class="app-content flash-animation"
                    style="transform-origin: 100% 0%; filter: contrast(110%) brightness(120%) hue-rotate(10deg); mix-blend-mode: color-dodge; mask-image: radial-gradient(300% 400px at 100% 40%, transparent 0%, rgb(0, 0, 0) 300%, transparent 300%); transform: none; opacity: 0;">
                    <div class="app-grid-container">
                        <div class="app-grid">
                            <div class="app-icon" tabindex="0">
                                <div class="app-icon-inner" style="background-color: rgb(52, 199, 89);"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="lucide lucide-message-square-icon lucide-message-square">
                                        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                                    </svg></div>
                                <p class="app-label">Messages</p>
                            </div>
                            <div class="app-icon" tabindex="0">
                                <div class="app-icon-inner" style="background-color: rgb(255, 149, 0);"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-calendar-icon lucide-calendar">
                                        <path d="M8 2v4"></path>
                                        <path d="M16 2v4"></path>
                                        <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                                        <path d="M3 10h18"></path>
                                    </svg></div>
                                <p class="app-label">Calendar</p>
                            </div>
                            <div class="app-icon" tabindex="0">
                                <div class="app-icon-inner" style="background-color: rgb(175, 82, 222);"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-image-icon lucide-image">
                                        <rect width="18" height="18" x="3" y="3" rx="2" ry="2"></rect>
                                        <circle cx="9" cy="9" r="2"></circle>
                                        <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"></path>
                                    </svg></div>
                                <p class="app-label">Photos</p>
                            </div>
                            <div class="app-icon" tabindex="0">
                                <div class="app-icon-inner" style="background-color: rgb(90, 200, 250);"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-camera-icon lucide-camera">
                                        <path
                                            d="M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z">
                                        </path>
                                        <circle cx="12" cy="13" r="3"></circle>
                                    </svg></div>
                                <p class="app-label">Camera</p>
                            </div>
                            <div class="app-icon" tabindex="0">
                                <div class="app-icon-inner" style="background-color: rgb(255, 45, 85);"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-map-icon lucide-map">
                                        <path
                                            d="M14.106 5.553a2 2 0 0 0 1.788 0l3.659-1.83A1 1 0 0 1 21 4.619v12.764a1 1 0 0 1-.553.894l-4.553 2.277a2 2 0 0 1-1.788 0l-4.212-2.106a2 2 0 0 0-1.788 0l-3.659 1.83A1 1 0 0 1 3 19.381V6.618a1 1 0 0 1 .553-.894l4.553-2.277a2 2 0 0 1 1.788 0z">
                                        </path>
                                        <path d="M15 5.764v15"></path>
                                        <path d="M9 3.236v15"></path>
                                    </svg></div>
                                <p class="app-label">Maps</p>
                            </div>
                            <div class="app-icon" tabindex="0">
                                <div class="app-icon-inner" style="background-color: rgb(255, 149, 0);"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-cloud-icon lucide-cloud">
                                        <path d="M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z"></path>
                                    </svg></div>
                                <p class="app-label">Weather</p>
                            </div>
                            <div class="app-icon" tabindex="0">
                                <div class="app-icon-inner" style="background-color: rgb(0, 0, 0);"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-wallet-icon lucide-wallet">
                                        <path
                                            d="M19 7V4a1 1 0 0 0-1-1H5a2 2 0 0 0 0 4h15a1 1 0 0 1 1 1v4h-3a2 2 0 0 0 0 4h3a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1">
                                        </path>
                                        <path d="M3 5v14a2 2 0 0 0 2 2h15a1 1 0 0 0 1-1v-4"></path>
                                    </svg></div>
                                <p class="app-label">Wallet</p>
                            </div>
                            <div class="app-icon" tabindex="0">
                                <div class="app-icon-inner" style="background-color: rgb(255, 204, 0);"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-file-text-icon lucide-file-text">
                                        <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path>
                                        <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
                                        <path d="M10 9H8"></path>
                                        <path d="M16 13H8"></path>
                                        <path d="M16 17H8"></path>
                                    </svg></div>
                                <p class="app-label">Notes</p>
                            </div>
                            <div class="app-icon" tabindex="0">
                                <div class="app-icon-inner" style="background-color: rgb(255, 59, 48);"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="lucide lucide-square-check-big-icon lucide-square-check-big">
                                        <path d="M21 10.5V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h12.5"></path>
                                        <path d="m9 11 3 3L22 4"></path>
                                    </svg></div>
                                <p class="app-label">Reminders</p>
                            </div>
                            <div class="app-icon" tabindex="0">
                                <div class="app-icon-inner" style="background-color: rgb(88, 86, 214);"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-clock-icon lucide-clock">
                                        <circle cx="12" cy="12" r="10"></circle>
                                        <polyline points="12 6 12 12 16 14"></polyline>
                                    </svg></div>
                                <p class="app-label">Clock</p>
                            </div>
                            <div class="app-icon" tabindex="0">
                                <div class="app-icon-inner" style="background-color: rgb(255, 45, 85);"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-newspaper-icon lucide-newspaper">
                                        <path d="M15 18h-5"></path>
                                        <path d="M18 14h-8"></path>
                                        <path
                                            d="M4 22h16a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v16a2 2 0 0 1-4 0v-9a2 2 0 0 1 2-2h2">
                                        </path>
                                        <rect x="10" y="6" width="8" height="4" rx="1"></rect>
                                    </svg></div>
                                <p class="app-label">News</p>
                            </div>
                            <div class="app-icon" tabindex="0">
                                <div class="app-icon-inner" style="background-color: rgb(0, 122, 255);"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="lucide lucide-shopping-bag-icon lucide-shopping-bag">
                                        <path d="M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z"></path>
                                        <path d="M3 6h18"></path>
                                        <path d="M16 10a4 4 0 0 1-8 0"></path>
                                    </svg></div>
                                <p class="app-label">App Store</p>
                            </div>
                            <div class="app-icon" tabindex="0">
                                <div class="app-icon-inner" style="background-color: rgb(255, 45, 85);"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-heart-icon lucide-heart">
                                        <path
                                            d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z">
                                        </path>
                                    </svg></div>
                                <p class="app-label">Health</p>
                            </div>
                            <div class="app-icon" tabindex="0">
                                <div class="app-icon-inner" style="background-color: rgb(142, 142, 147);"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-settings-icon lucide-settings">
                                        <path
                                            d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z">
                                        </path>
                                        <circle cx="12" cy="12" r="3"></circle>
                                    </svg></div>
                                <p class="app-label">Settings</p>
                            </div>
                            <div class="app-icon" tabindex="0">
                                <div class="app-icon-inner" style="background-color: rgb(0, 122, 255);"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-compass-icon lucide-compass">
                                        <path
                                            d="m16.24 7.76-1.804 5.411a2 2 0 0 1-1.265 1.265L7.76 16.24l1.804-5.411a2 2 0 0 1 1.265-1.265z">
                                        </path>
                                        <circle cx="12" cy="12" r="10"></circle>
                                    </svg></div>
                                <p class="app-label">Safari</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

</html>