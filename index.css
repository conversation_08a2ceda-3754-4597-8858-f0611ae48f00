body {
    overflow: hidden;
    margin: 0;
    padding: 0;
    background-color: #f0f0f0;
}
.iphone-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100vh;
    padding: 20px;
    box-sizing: border-box;
}
.iphone-mock {
    position: relative;
    width: 375px;
    height: 812px;
    background-color: #1a1a1a;
    border-radius: 50px;
    box-shadow: 0 0 0 14px #121212, 0 0 0 17px #232323, 0 20px 40px rgba(0, 0, 0, 0.8);
    padding: 0;
    box-sizing: border-box;
    overflow: hidden;
}
@media (max-height: 900px) {
.iphone-mock {
        scale: 0.7;
}
}
@media (max-height: 600px) {
.iphone-mock {
        scale: 0.4;
}
}
.iphone-screen {
    position: relative;
    width: 100%;
    height: 100%;
    background-color: black;
    border-radius: 38px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}
.dynamic-island {
    position: absolute;
    top: 12px;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
    height: 34px;
    background-color: #000;
    border-radius: 20px;
    z-index: 2000;
}
.iphone-home-indicator {
    position: absolute;
    bottom: 8px;
    left: 50%;
    transform: translateX(-50%);
    width: 134px;
    height: 5px;
    background-color: white;
    opacity: 0.2;
    border-radius: 3px;
    z-index: 2000;
}
.app-content {
    flex: 1;
    padding: 20px;
    padding-top: 50px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background-image: url(kirby.jpg);
    background-size: cover;
    background-position: center;
    box-sizing: border-box;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}
.app-grid-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    color: #f5f5f5;
}
.app-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    padding: 25px 15px;
}
.app-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}
.app-icon-inner {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
    font-weight: bold;
    color: white;
}
.app-label {
    font-size: 12px;
    margin: 0;
    text-align: center;
    color: #f5f5f5;
}

/* 闪光特效动画 */
@keyframes flashEffect {
    0% {
        transform-origin: 100% 0%;
        filter: contrast(110%) brightness(120%) hue-rotate(10deg);
        mix-blend-mode: color-dodge;
        mask-image: radial-gradient(300% 400px at 100% 40%, transparent 0%, rgb(0, 0, 0) 300%, transparent 300%);
        transform: none;
        opacity: 0;
    }

    10% {
        transform-origin: 50% 50%;
        filter: contrast(200%) brightness(300%) hue-rotate(45deg);
        mix-blend-mode: screen;
        mask-image: radial-gradient(150% 200px at 50% 50%, transparent 0%, rgb(255, 255, 255) 150%, transparent 150%);
        transform: scale(1.1) rotate(2deg);
        opacity: 0.8;
    }

    25% {
        transform-origin: 0% 100%;
        filter: contrast(300%) brightness(500%) hue-rotate(90deg);
        mix-blend-mode: overlay;
        mask-image: radial-gradient(100% 100px at 0% 100%, transparent 0%, rgb(255, 255, 255) 100%, transparent 100%);
        transform: scale(1.2) rotate(-1deg);
        opacity: 1;
    }

    50% {
        transform-origin: 50% 0%;
        filter: contrast(400%) brightness(800%) hue-rotate(180deg);
        mix-blend-mode: hard-light;
        mask-image: radial-gradient(200% 300px at 50% 0%, transparent 0%, rgb(255, 255, 255) 200%, transparent 200%);
        transform: scale(0.9) rotate(3deg);
        opacity: 0.9;
    }

    75% {
        transform-origin: 100% 50%;
        filter: contrast(250%) brightness(400%) hue-rotate(270deg);
        mix-blend-mode: difference;
        mask-image: radial-gradient(250% 350px at 100% 50%, transparent 0%, rgb(128, 128, 128) 250%, transparent 250%);
        transform: scale(1.05) rotate(-2deg);
        opacity: 0.6;
    }

    90% {
        transform-origin: 25% 75%;
        filter: contrast(150%) brightness(200%) hue-rotate(320deg);
        mix-blend-mode: multiply;
        mask-image: radial-gradient(180% 250px at 25% 75%, transparent 0%, rgb(64, 64, 64) 180%, transparent 180%);
        transform: scale(1.02) rotate(1deg);
        opacity: 0.3;
    }

    100% {
        transform-origin: 100% 0%;
        filter: contrast(110%) brightness(120%) hue-rotate(10deg);
        mix-blend-mode: color-dodge;
        mask-image: radial-gradient(300% 400px at 100% 40%, transparent 0%, rgb(0, 0, 0) 300%, transparent 300%);
        transform: none;
        opacity: 0;
    }
}

/* 应用动画的类 */
.flash-animation {
    animation: flashEffect 2s ease-in-out infinite;
}