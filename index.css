body {
    overflow: hidden;
    margin: 0;
    padding: 0;
    background-color: #f0f0f0;
}
.iphone-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100vh;
    padding: 20px;
    box-sizing: border-box;
}
.iphone-mock {
    position: relative;
    width: 375px;
    height: 812px;
    background-color: #1a1a1a;
    border-radius: 50px;
    box-shadow: 0 0 0 14px #121212, 0 0 0 17px #232323, 0 20px 40px rgba(0, 0, 0, 0.8);
    padding: 0;
    box-sizing: border-box;
    overflow: hidden;
}
@media (max-height: 900px) {
.iphone-mock {
        scale: 0.7;
}
}
@media (max-height: 600px) {
.iphone-mock {
        scale: 0.4;
}
}
.iphone-screen {
    position: relative;
    width: 100%;
    height: 100%;
    background-color: black;
    border-radius: 38px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}
.dynamic-island {
    position: absolute;
    top: 12px;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
    height: 34px;
    background-color: #000;
    border-radius: 20px;
    z-index: 2000;
}
.iphone-home-indicator {
    position: absolute;
    bottom: 8px;
    left: 50%;
    transform: translateX(-50%);
    width: 134px;
    height: 5px;
    background-color: white;
    opacity: 0.2;
    border-radius: 3px;
    z-index: 2000;
}
.app-content {
    flex: 1;
    padding: 20px;
    padding-top: 50px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background-image: url(kirby.jpg);
    background-size: cover;
    background-position: center;
    box-sizing: border-box;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}
.app-grid-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    color: #f5f5f5;
}
.app-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    padding: 25px 15px;
}
.app-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}
.app-icon-inner {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
    font-weight: bold;
    color: white;
}
.app-label {
    font-size: 12px;
    margin: 0;
    text-align: center;
    color: #f5f5f5;
}