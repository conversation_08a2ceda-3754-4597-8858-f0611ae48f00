body {
    overflow: hidden;
    margin: 0;
    padding: 0;
    background-color: #f0f0f0;
}
.iphone-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100vh;
    padding: 20px;
    box-sizing: border-box;
}
.iphone-mock {
    position: relative;
    width: 375px;
    height: 812px;
    background-color: #1a1a1a;
    border-radius: 50px;
    box-shadow: 0 0 0 14px #121212, 0 0 0 17px #232323, 0 20px 40px rgba(0, 0, 0, 0.8);
    padding: 0;
    box-sizing: border-box;
    overflow: hidden;
}
@media (max-height: 900px) {
.iphone-mock {
        scale: 0.7;
}
}
@media (max-height: 600px) {
.iphone-mock {
        scale: 0.4;
}
}
.iphone-screen {
    position: relative;
    width: 100%;
    height: 100%;
    background-color: black;
    border-radius: 38px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}
.dynamic-island {
    position: absolute;
    top: 12px;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
    height: 34px;
    background-color: #000;
    border-radius: 20px;
    z-index: 2000;
}
.iphone-home-indicator {
    position: absolute;
    bottom: 8px;
    left: 50%;
    transform: translateX(-50%);
    width: 134px;
    height: 5px;
    background-color: white;
    opacity: 0.2;
    border-radius: 3px;
    z-index: 2000;
}
.app-content {
    flex: 1;
    padding: 20px;
    padding-top: 50px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background-image: url(kirby.jpg);
    background-size: cover;
    background-position: center;
    box-sizing: border-box;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}
.app-grid-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    color: #f5f5f5;
}
.app-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    padding: 25px 15px;
}
.app-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}
.app-icon-inner {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
    font-weight: bold;
    color: white;
}
.app-label {
    font-size: 12px;
    margin: 0;
    text-align: center;
    color: #f5f5f5;
}

/* 波浪扫过特效动画 */
@keyframes waveEffect {
    0% {
        transform-origin: 100% 0%;
        filter: contrast(110%) brightness(120%) hue-rotate(10deg);
        mix-blend-mode: color-dodge;
        mask-image: linear-gradient(90deg, transparent 0%, transparent 100%);
        transform: none;
        opacity: 0;
    }

    10% {
        transform-origin: 100% 0%;
        filter: contrast(150%) brightness(180%) hue-rotate(25deg);
        mix-blend-mode: color-dodge;
        mask-image: linear-gradient(90deg, transparent 0%, transparent 80%, rgba(255,255,255,0.3) 85%, rgba(255,255,255,0.8) 90%, rgba(255,255,255,0.3) 95%, transparent 100%);
        transform: none;
        opacity: 0.3;
    }

    25% {
        transform-origin: 100% 0%;
        filter: contrast(200%) brightness(280%) hue-rotate(45deg);
        mix-blend-mode: screen;
        mask-image: linear-gradient(90deg, transparent 0%, transparent 60%, rgba(255,255,255,0.4) 65%, rgba(255,255,255,1) 70%, rgba(255,255,255,0.4) 75%, transparent 80%, transparent 100%);
        transform: none;
        opacity: 0.6;
    }

    40% {
        transform-origin: 100% 0%;
        filter: contrast(280%) brightness(400%) hue-rotate(70deg);
        mix-blend-mode: overlay;
        mask-image: linear-gradient(90deg, transparent 0%, transparent 40%, rgba(255,255,255,0.5) 45%, rgba(255,255,255,1) 50%, rgba(255,255,255,0.5) 55%, transparent 60%, transparent 100%);
        transform: none;
        opacity: 0.9;
    }

    55% {
        transform-origin: 100% 0%;
        filter: contrast(350%) brightness(550%) hue-rotate(95deg);
        mix-blend-mode: hard-light;
        mask-image: linear-gradient(90deg, transparent 0%, transparent 20%, rgba(255,255,255,0.6) 25%, rgba(255,255,255,1) 30%, rgba(255,255,255,0.6) 35%, transparent 40%, transparent 100%);
        transform: none;
        opacity: 1;
    }

    70% {
        transform-origin: 100% 0%;
        filter: contrast(280%) brightness(400%) hue-rotate(70deg);
        mix-blend-mode: overlay;
        mask-image: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.5) 5%, rgba(255,255,255,1) 10%, rgba(255,255,255,0.5) 15%, transparent 20%, transparent 100%);
        transform: none;
        opacity: 0.7;
    }

    85% {
        transform-origin: 100% 0%;
        filter: contrast(180%) brightness(220%) hue-rotate(35deg);
        mix-blend-mode: color-dodge;
        mask-image: linear-gradient(90deg, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0.6) 2%, rgba(255,255,255,0.3) 5%, transparent 10%, transparent 100%);
        transform: none;
        opacity: 0.4;
    }

    100% {
        transform-origin: 100% 0%;
        filter: contrast(110%) brightness(120%) hue-rotate(10deg);
        mix-blend-mode: color-dodge;
        mask-image: linear-gradient(90deg, transparent 0%, transparent 100%);
        transform: none;
        opacity: 0;
    }
}

/* 应用动画的类 */
.wave-animation {
    animation: waveEffect 3s ease-in-out infinite;
}